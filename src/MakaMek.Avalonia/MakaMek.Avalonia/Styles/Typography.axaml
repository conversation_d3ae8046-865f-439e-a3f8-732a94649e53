<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Heading Styles -->
    <Style Selector="TextBlock.h1">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.h2">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.h3">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,6"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.h4">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <!-- Body Text Styles -->
    <Style Selector="TextBlock.body">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.bodyLarge">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.bodySmall">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{DynamicResource TextLightBrush}"/>
    </Style>
    
    <!-- Label Styles -->
    <Style Selector="TextBlock.labelLarge">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.label">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <Style Selector="TextBlock.labelSmall">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource TextLightBrush}"/>
    </Style>
    
    <!-- Caption Styles -->
    <Style Selector="TextBlock.caption">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource TextLightBrush}"/>
    </Style>
    
    <!-- Game-Specific Text Styles -->
    <Style Selector="TextBlock.turnInfo">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <Style Selector="TextBlock.turnInfoLabel">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <Style Selector="TextBlock.actionInfo">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
</Styles>

